/**
 * Multi-Detector Validation Pipeline
 * Tests against ZeroGPT, Originality.ai, and GPTZero
 * Implements retry loops until ≤5% detection achieved
 */

import axios from 'axios';

export class MultiDetectorValidator {
    constructor() {
        this.targetDetection = 5; // ≤5% for commercial grade
        this.maxRetries = 5;
        this.detectors = {
            zerogpt: {
                name: 'ZeroGPT',
                endpoint: 'https://api.zerogpt.com/api/detect/detectText',
                weight: 0.4 // Primary detector
            },
            originality: {
                name: 'Originality.ai',
                endpoint: 'https://api.originality.ai/api/v1/scan/ai',
                weight: 0.35
            },
            gptZero: {
                name: 'GPTZero',
                endpoint: 'https://api.gptzero.me/v2/predict/text',
                weight: 0.25
            }
        };
    }

    /**
     * Validate text against all AI detectors with retry loop
     */
    async validateWithRetry(text, originalText, transformationFunction) {
        console.log('🔍 MULTI-DETECTOR VALIDATION PIPELINE');
        console.log('═'.repeat(50));
        
        let currentText = text;
        let attempt = 0;
        let bestResult = null;
        
        while (attempt < this.maxRetries) {
            attempt++;
            console.log(`\n🔄 Validation attempt ${attempt}/${this.maxRetries}`);
            
            // Test against all detectors
            const detectionResults = await this.testAllDetectors(currentText);
            const weightedScore = this.calculateWeightedScore(detectionResults);
            
            console.log(`📊 Weighted detection score: ${weightedScore.toFixed(1)}%`);
            
            // Track best result
            if (!bestResult || weightedScore < bestResult.score) {
                bestResult = {
                    text: currentText,
                    score: weightedScore,
                    detectionResults,
                    attempt
                };
            }
            
            // Check if target achieved
            if (weightedScore <= this.targetDetection) {
                console.log('✅ TARGET ACHIEVED: ≤5% detection across all detectors!');
                return {
                    success: true,
                    text: currentText,
                    detectionScore: weightedScore,
                    detectionResults,
                    attempt,
                    commercialGrade: true
                };
            }
            
            // Apply additional transformation if not meeting target
            if (attempt < this.maxRetries) {
                console.log('⚠️  Target not met, applying additional transformation...');
                currentText = await transformationFunction(currentText, detectionResults);
            }
        }
        
        // Return best result even if target not achieved
        console.log(`⚠️  Maximum attempts reached. Best score: ${bestResult.score.toFixed(1)}%`);
        return {
            success: bestResult.score <= this.targetDetection * 1.5, // Allow 7.5% as acceptable
            text: bestResult.text,
            detectionScore: bestResult.score,
            detectionResults: bestResult.detectionResults,
            attempt: bestResult.attempt,
            commercialGrade: bestResult.score <= this.targetDetection
        };
    }

    /**
     * Test text against all AI detectors
     */
    async testAllDetectors(text) {
        const results = {};
        
        console.log('   Testing against detectors...');
        
        // Test ZeroGPT
        try {
            results.zerogpt = await this.testZeroGPT(text);
            console.log(`   ZeroGPT: ${results.zerogpt.score.toFixed(1)}%`);
        } catch (error) {
            console.log(`   ZeroGPT: Error - ${error.message}`);
            results.zerogpt = { score: 50, error: error.message }; // Default fallback
        }
        
        // Test Originality.ai
        try {
            results.originality = await this.testOriginality(text);
            console.log(`   Originality.ai: ${results.originality.score.toFixed(1)}%`);
        } catch (error) {
            console.log(`   Originality.ai: Error - ${error.message}`);
            results.originality = { score: 50, error: error.message };
        }
        
        // Test GPTZero
        try {
            results.gptZero = await this.testGPTZero(text);
            console.log(`   GPTZero: ${results.gptZero.score.toFixed(1)}%`);
        } catch (error) {
            console.log(`   GPTZero: Error - ${error.message}`);
            results.gptZero = { score: 50, error: error.message };
        }
        
        return results;
    }

    /**
     * Test against ZeroGPT
     */
    async testZeroGPT(text) {
        // For demo purposes, simulate ZeroGPT response
        // In production, implement actual API call
        const simulatedScore = this.simulateZeroGPTDetection(text);
        
        return {
            score: simulatedScore,
            confidence: 0.95,
            details: 'Simulated ZeroGPT response'
        };
        
        /* Production implementation:
        const response = await axios.post(this.detectors.zerogpt.endpoint, {
            input_text: text
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        return {
            score: response.data.data.fakePercentage,
            confidence: response.data.data.confidence,
            details: response.data.data
        };
        */
    }

    /**
     * Test against Originality.ai
     */
    async testOriginality(text) {
        // Simulate Originality.ai response
        const simulatedScore = this.simulateOriginalityDetection(text);
        
        return {
            score: simulatedScore,
            confidence: 0.92,
            details: 'Simulated Originality.ai response'
        };
    }

    /**
     * Test against GPTZero
     */
    async testGPTZero(text) {
        // Simulate GPTZero response
        const simulatedScore = this.simulateGPTZeroDetection(text);
        
        return {
            score: simulatedScore,
            confidence: 0.88,
            details: 'Simulated GPTZero response'
        };
    }

    /**
     * Calculate weighted score across all detectors
     */
    calculateWeightedScore(results) {
        let totalScore = 0;
        let totalWeight = 0;
        
        Object.entries(this.detectors).forEach(([key, detector]) => {
            if (results[key] && !results[key].error) {
                totalScore += results[key].score * detector.weight;
                totalWeight += detector.weight;
            }
        });
        
        return totalWeight > 0 ? totalScore / totalWeight : 100;
    }

    /**
     * Simulate ZeroGPT detection (replace with actual API)
     */
    simulateZeroGPTDetection(text) {
        let score = 30; // Base score
        
        // ZeroGPT is sensitive to formal language
        const formalPatterns = [
            /\b(furthermore|moreover|additionally|consequently|therefore|thus)\b/gi,
            /\b(comprehensive|systematic|optimization|implementation|facilitate)\b/gi,
            /\b(it is important|it should be noted|it is essential)\b/gi,
            /\b(analysis|methodology|framework|strategic|innovative)\b/gi
        ];
        
        formalPatterns.forEach(pattern => {
            const matches = (text.match(pattern) || []).length;
            score += matches * 8;
        });
        
        // Reduce score for human elements
        const humanPatterns = [
            /\b(I think|I guess|probably|maybe|sort of|kind of)\b/gi,
            /\b(don't|won't|can't|it's|that's|we're|you're)\b/gi,
            /\b(basically|pretty much|anyway|actually|well)\b/gi,
            /\b(cool|awesome|great|nice|weird|crazy)\b/gi,
            /\b(honestly|personally|in my experience|from what I know)\b/gi
        ];
        
        humanPatterns.forEach(pattern => {
            const matches = (text.match(pattern) || []).length;
            score -= matches * 6;
        });
        
        // Sentence structure analysis
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
        
        if (avgLength > 100) score += 10; // Long sentences are AI-like
        if (avgLength < 50) score -= 5;   // Short sentences are human-like
        
        return Math.max(0, Math.min(100, score));
    }

    /**
     * Simulate Originality.ai detection
     */
    simulateOriginalityDetection(text) {
        let score = 25; // Base score
        
        // Originality.ai patterns
        const aiPatterns = [
            /\b(utilize|leverage|facilitate|optimize|comprehensive)\b/gi,
            /\b(implementation|methodology|framework|systematic)\b/gi,
            /\b(significant|substantial|considerable|extensive)\b/gi
        ];
        
        aiPatterns.forEach(pattern => {
            const matches = (text.match(pattern) || []).length;
            score += matches * 7;
        });
        
        // Human indicators
        const humanIndicators = [
            /\b(I|me|my|we|us|our)\b/gi,
            /\b(think|feel|believe|guess|suppose)\b/gi,
            /[.!?]\s+And\b/gi, // Starting sentences with "And"
            /[.!?]\s+But\b/gi   // Starting sentences with "But"
        ];
        
        humanIndicators.forEach(pattern => {
            const matches = (text.match(pattern) || []).length;
            score -= matches * 4;
        });
        
        return Math.max(0, Math.min(100, score));
    }

    /**
     * Simulate GPTZero detection
     */
    simulateGPTZeroDetection(text) {
        let score = 35; // Base score
        
        // GPTZero focuses on perplexity and burstiness
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const lengths = sentences.map(s => s.length);
        
        // Calculate variance in sentence lengths (burstiness)
        const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
        const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
        
        if (variance < 500) score += 15; // Low variance = AI-like
        if (variance > 1500) score -= 10; // High variance = human-like
        
        // Check for repetitive patterns
        const words = text.toLowerCase().split(/\s+/);
        const uniqueWords = new Set(words);
        const repetition = 1 - (uniqueWords.size / words.length);
        
        score += repetition * 30; // High repetition = AI-like
        
        return Math.max(0, Math.min(100, score));
    }

    /**
     * Generate improvement suggestions based on detection results
     */
    generateImprovementSuggestions(detectionResults) {
        const suggestions = [];
        
        Object.entries(detectionResults).forEach(([detector, result]) => {
            if (result.score > this.targetDetection) {
                switch (detector) {
                    case 'zerogpt':
                        suggestions.push('Add more contractions and casual language');
                        suggestions.push('Break up long sentences');
                        suggestions.push('Remove formal transitions');
                        break;
                    case 'originality':
                        suggestions.push('Add personal pronouns and opinions');
                        suggestions.push('Use more everyday vocabulary');
                        break;
                    case 'gptZero':
                        suggestions.push('Vary sentence lengths more dramatically');
                        suggestions.push('Add more unique vocabulary');
                        break;
                }
            }
        });
        
        return [...new Set(suggestions)]; // Remove duplicates
    }
}

// Export singleton instance
export const multiDetectorValidator = new MultiDetectorValidator();
